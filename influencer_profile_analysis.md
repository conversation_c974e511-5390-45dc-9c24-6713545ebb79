# NoxInfluencer 网红详情页需求文档 (根据截图分析)

本文档基于提供的截图,详细梳理 NoxInfluencer 网红详情页的四大核心模块:数据总览、内容数据、受众数据和品牌数据,定义各模块下的具体数据指标。

---

## 一、 数据总览 (Data Overview)

本模块提供网红的宏观数据,用于快速评估其整体表现和商业价值。

### 1.1 网红基本信息 (Influencer Basic Info)
- **网红头像与名称**: `<PERSON> (@realbarbarapalvin)`
- **国家/地区**: 捷克共和国
- **核心统计**:
  - **粉丝数**: 11.15万
  - **最近发布时间**: 10天前
  - **最近推广时间**: 127天前
  - **Nox评分**: 2.7 / 5
  - **合作倾向**: 2 / 10

### 1.2 基本数据 (Basic Metrics - 近30个内容)
- **平均观看量**: 10.1万
- **平均互动量**: 5507
- **内容数**: 30
- **观看量/粉丝数比例**: 90.59%

### 1.3 增长数据 (Growth Data)
- **关键指标总览**:
  - **粉丝数**: 111,500
  - **点赞数**: 1,800,000
  - **视频数**: 242
- **增长趋势图**: 提供粉丝数、点赞量、发布量的随时间变化的增长曲线图。

### 1.4 频道质量 (Channel Quality)
- **Nox评分**: 2.7 / 5 (中等)
- **Nox评分雷达图**: 从以下六个维度评估频道质量:
  - 粉丝增长
  - 创作频率
  - 频道质量
  - 互动率
  - 粉丝可信度
  - 合作率

### 1.5 合作分析 (Collaboration Analysis)
- **合作倾向**: 2 / 10 (合作概率相对较低)
- **合作行为标签**: 如 `近90天无内容`、`有联系方式` 等。
- **合作价格与CPM (Cost Per Mille)**:
  - **CPM**: $22.5
  - **植入视频预估价格**: $1610 - $2187
- **频道粉丝数排名**:
  - **世界排名**: 1,457,910
  - **捷克共和国排名**: 2,617

---

## 二、 内容数据 (Content Data)

本模块深入分析网红的内容创作表现和互动情况。

### 2.1 基本数据 (Basic Metrics - 近30个内容)
- **互动率**: 6.13% (及格)
- **观看量/粉丝数**: 94.85% (优秀)
- **点赞数/观看数**: 5.47% (中等)
- **评论数/观看数**: 0.04% (中等)
- **分享数/观看数**: 0.08% (良好)

### 2.2 互动趋势 (Interaction Trend)
- **多维度互动图表**: 展示一段时间内,视频的 **观看数、点赞数、评论数、分享数** 的变化情况。

### 2.3 发布分析 (Publishing Analysis)
- **发布时间表**: 以日历形式展示指定月份的视频发布日期。
- **发布频率**: 按周一至周日统计视频发布的频率分布,揭示网红最活跃的发布日。

### 2.4 频道标签 (Channel Tags)
- **标签云**: 直观展示网红最常用的视频标签。
- **Top 5 主要标签**: 列出使用频率最高的5个标签及其占比。
  - `#barbarapalvin`: 11.4%
  - `#barbara`: 11.4%
  - `#foryoupage`: 8.1%
  - `#photoshoot`: 7.3%

### 2.5 内容列表 (Content List)
- **内容统计**: 
  - **已收录作品数**: 137
  - **总浏览量**: 1588.13万
  - **总点赞量**: 128.56万
  - **总评论数**: 6039
- **视频流**: 以卡片形式展示视频,包含封面、发布日期、时长、观看量、点赞数、评论数。

---

## 三、 受众数据 (Audience Data)

本模块分析粉丝画像,帮助评估粉丝群体与品牌目标的匹配度。

### 3.1 核心受众指标 (Core Audience Metrics)
- **粉丝可信度**: 2.5 / 5 (中等)
- **最多受众区域**: 捷克共和国
- **最多受众性别**: 女性 (77.8%)
- **最多受众年龄**: 25-34岁 (35.3%)

### 3.2 受众特征 (Audience Profile)
- **受众区域**: 世界地图和条形图,展示粉丝的国家/地区分布及占比。
- **受众语言**: 条形图展示粉丝使用的主要语言及占比 (如丹麦语 52.9%, 英语 47.1%)。

### 3.3 年龄与性别 (Age & Gender)
- **性别分布**: 环形图展示女性 (77.8%) vs 男性 (22.2%) 的粉丝比例。
- **年龄统计**: 柱状图展示各年龄段 (13-17, 18-24, 25-34, etc.) 的粉丝分布。

### 3.4 营销分析 (Marketing Analysis)
- **受众反馈**:
  - **正向反馈的受众**: 69%
  - **推荐内容感兴趣的受众**: 65%
- **消费影响力**:
  - **推广吸引度**: 评估推广内容的吸引力 (3/5星)。
  - **推广专业度**: 评估推广内容的专业性 (3/5星)。

### 3.5 内容与兴趣 (Content & Interests)
- **内容分布**: 环形图展示粉丝感兴趣的内容分类及占比 (如彩妆 60%, 街拍 13.3% 等)。
- **兴趣点描述**: 对各类兴趣进行文字描述。

### 3.6 粉丝可信度 (Follower Authenticity)
- **受众类型**: 环形图分析粉丝构成。
  - **普通粉**: 80%
  - **可疑粉**: 11.9%
  - **僵尸粉**: 5.6%
  - **网红粉**: 2.5%
- **真实受众**: 综合评估的真实粉丝比例为 **82.5%**。

---

## 四、 品牌数据 (Brand Data)

本模块聚焦于网红的商业合作表现和潜力。

### 4.1 基本数据 (Basic Metrics)
- **品牌广告效果 (互动率)**: 0 (近期无品牌广告)
- **发布品牌频次**: -- / 每月

### 4.2 带货推广 vs. 推广 (Branded vs. Non-Branded)
- **观看量对比图**: 以图表形式对比 **推广内容** 和 **非推广内容** 的平均观看量。

### 4.3 品牌提及 (Brand Mentions)
- **提及品牌列表**: 表格形式列出历史合作或提及过的品牌。
  - **品牌**: 品牌Logo和名称 (e.g., Victoria's Secret, Michael Kors)。
  - **推广次数**: 合作视频数量。
  - **互动率**: 合作视频的平均互动率。
  - **总观看量**: 合作视频的总观看量。
  - **上次视频时间**: 最近一次合作视频的发布日期。
  - **预算**: 预估合作费用。
  - **提及视频**: 展示相关的视频封面。

### 4.4 品牌提及智能识别需求
- **LLM自动提取**: 需要通过大模型分析达人过往视频内容，自动识别品牌提及。
- **数据输入要求**: 视频标题、描述、字幕、评论等文本内容。
- **输出格式**: 结构化的品牌提及数据，包括品牌名称、提及类型、置信度等。
- **更新频率**: 新发布视频后自动分析，历史视频按优先级批量处理。

---

## 五、 数据表结构定义 (Data Table Schema)

### 5.1 `influencer_overview` (网红总览表)
| 字段 (Field Name) | 数据类型 (Data Type) | 描述 (Description) | 示例值 (Example Value) |
| :--- | :--- | :--- | :--- |
| `influencer_id` | `String` | 网红唯一标识 | `@realbarbarapalvin` |
| `avatar_url` | `String` | 头像图片链接 | `https://...` |
| `country` | `String` | 国家/地区 | `捷克共和国` |
| `total_followers` | `Integer` | 总粉丝数 | `111500` |
| `latest_post_date` | `Date` | 最近发布时间 | `2025-07-08` |
| `latest_promo_date` | `Date` | 最近推广时间 | `2025-03-08` |
| `nox_score` | `Float` | Nox综合评分 | `2.7` |
| `collaboration_score` | `Integer` | 合作倾向评分 | `2` |
| `avg_views_30d` | `Integer` | 近30天平均观看 | `101000` |
| `avg_engagement_30d` | `Integer` | 近30天平均互动 | `5507` |
| `views_per_follower_ratio` | `Float` | 观看/粉丝比 | `0.9059` |
| `total_likes` | `Integer` | 累计点赞数 | `1800000` |
| `total_videos` | `Integer` | 累计视频数 | `242` |
| `quality_radar_scores` | `JSON` | 质量雷达图六项评分 | `{"growth": 3, ...}` |
| `estimated_cpm` | `Float` | 预估CPM价格 | `22.5` |
| `estimated_video_price` | `String` | 视频植入预估价格范围 | `1610-2187` |
| `rank_world` | `Integer` | 粉丝数世界排名 | `1457910` |
| `rank_country` | `Integer` | 粉丝数国家排名 | `2617` |

### 5.2 `influencer_content` (内容数据表)
| 字段 (Field Name) | 数据类型 (Data Type) | 描述 (Description) | 示例值 (Example Value) |
| :--- | :--- | :--- | :--- |
| `influencer_id` | `String` | 网红唯一标识 | `@realbarbarapalvin` |
| `engagement_rate_30d` | `Float` | 近30天互动率 | `0.0613` |
| `likes_per_view_30d` | `Float` | 近30天点赞/观看比 | `0.0547` |
| `comments_per_view_30d` | `Float` | 近30天评论/观看比 | `0.0004` |
| `shares_per_view_30d` | `Float` | 近30天分享/观看比 | `0.0008` |
| `interaction_trend_data` | `JSON` | 互动趋势图数据 | `[{"date": "...", "views": ...}]` |
| `publish_schedule` | `JSON` | 发布日历数据 | `{"2025-07-13": 1, ...}` |
| `publish_frequency` | `JSON` | 按周统计的发布频率 | `{"monday": 15.4, ...}` |
| `top_tags` | `JSON` | Top5标签及占比 | `[{"tag": "#barbarapalvin", "pct": 0.114}]` |
| `total_archived_videos` | `Integer` | 已收录作品总数 | `137` |
| `total_archived_views` | `Integer` | 已收录作品总观看 | `15881300` |

### 5.3 `influencer_audience` (受众数据表)
| 字段 (Field Name) | 数据类型 (Data Type) | 描述 (Description) | 示例值 (Example Value) |
| :--- | :--- | :--- | :--- |
| `influencer_id` | `String` | 网红唯一标识 | `@realbarbarapalvin` |
| `authenticity_score` | `Float` | 粉丝可信度评分 | `2.5` |
| `top_country` | `String` | 最多受众国家 | `捷克共和国` |
| `top_gender` | `String` | 最多受众性别 | `女性` |
| `top_age_group` | `String` | 最多受众年龄段 | `25-34` |
| `geo_distribution` | `JSON` | 受众国家/地区分布 | `[{"country": "USA", "pct": 0.211}]` |
| `language_distribution` | `JSON` | 受众语言分布 | `[{"lang": "丹麦语", "pct": 0.529}]` |
| `gender_distribution` | `JSON` | 性别分布 | `{"female": 0.778, "male": 0.222}` |
| `age_distribution` | `JSON` | 年龄分布 | `[{"range": "25-34", "pct": 0.353}]` |
| `positive_feedback_ratio` | `Float` | 正向反馈的受众比例 | `0.69` |
| `promo_interest_ratio` | `Float` | 推广内容感兴趣的受众比例 | `0.65` |
| `promo_attraction_score` | `Integer` | 推广吸引度评分 | `3` |
| `promo_professional_score`| `Integer` | 推广专业度评分 | `3` |
| `interest_distribution` | `JSON` | 粉丝兴趣分布 | `[{"interest": "彩妆", "pct": 0.60}]` |
| `follower_type_distribution`| `JSON` | 粉丝类型分布 | `{"normal": 0.80, "suspicious": 0.119, ...}` |
| `real_follower_ratio` | `Float` | 真实受众预估比例 | `0.825` |

### 5.4 `influencer_brands` (品牌数据表)
| 字段 (Field Name) | 数据类型 (Data Type) | 描述 (Description) | 示例值 (Example Value) |
| :--- | :--- | :--- | :--- |
| `influencer_id` | `String` | 网红唯一标识 | `@realbarbarapalvin` |
| `branded_ad_engagement` | `Float` | 品牌广告互动率 | `0` |
| `branded_ad_frequency` | `String` | 品牌广告发布频次 | `-- / 每月` |
| `branded_vs_non_branded` | `JSON` | 推广与非推广内容数据对比 | `{"branded_views": ..., "non_branded_views": ...}` |
| `mentioned_brands` | `JSON` | 提及/合作品牌列表 | `[{"brand": "Victoria's Secret", "count": 9, ...}]` |

---

## 六、 多平台账号关联需求分析

### 6.1 多平台账号展示需求
- **统一展示**: 在达人详情页显示该达人在多个平台的账号信息
- **平台切换**: 支持在不同平台数据间快速切换查看
- **数据对比**: 展示跨平台的数据对比分析

### 6.2 账号关联规则设计
基于现有数据库设计，账号关联规则如下：
- **主表关联**: 通过 `influencers` 表的 `associatedPlatforms` 字段记录关联平台
- **从表详情**: 通过 `platforms` 表的 `influencerId` 字段建立关联
- **主平台标识**: 通过 `mainPlatform` 字段确定主要展示平台

### 6.3 关联字段映射
| 数据库字段 | UI展示 | 作用 |
|-----------|--------|------|
| `associatedPlatforms` | 平台图标列表 | 显示该达人拥有的所有平台账号 |
| `mainPlatform` | 主要平台标识 | 默认显示的平台数据 |
| `platform` | 平台选择器 | 用于切换不同平台的详细数据 |
| `isActive` | 活跃状态标识 | 显示该平台是否活跃 |

---

## 七、 数据可视化需求规范

### 7.1 图表类型要求
- **环形图**: 用于性别分布、内容分布、粉丝类型等比例数据
- **柱状图**: 用于年龄分布、发布频率、品牌合作次数等对比数据
- **折线图**: 用于粉丝增长趋势、互动趋势等时序数据
- **雷达图**: 用于频道质量的多维度评估
- **热力图**: 用于发布时间分布、地理分布等密度数据
- **词云图**: 用于标签分布、关键词分析等文本数据

### 7.2 交互功能需求
- **数据钻取**: 支持点击图表元素查看详细数据
- **时间筛选**: 支持选择不同时间范围查看数据
- **数据导出**: 支持将图表数据导出为Excel或PDF
- **实时刷新**: 支持数据实时更新和图表重绘

### 7.3 响应式设计
- **移动适配**: 图表在移动设备上的自适应显示
- **加载状态**: 数据加载时的Loading状态显示
- **错误处理**: 数据异常时的友好提示

---

## 八、 品牌提及LLM分析需求

### 8.1 输入数据格式
```json
{
  "influencer_id": "string",
  "video_data": [
    {
      "video_id": "string",
      "title": "string",
      "description": "string",
      "subtitles": "string",
      "comments": ["string"],
      "publish_date": "date",
      "platform": "string"
    }
  ]
}
```

### 8.2 输出数据格式
```json
{
  "influencer_id": "string",
  "brand_mentions": [
    {
      "brand_name": "string",
      "brand_category": "string",
      "mention_type": "explicit|implicit|product_placement",
      "confidence_score": "float",
      "video_id": "string",
      "mention_context": "string",
      "sentiment": "positive|neutral|negative",
      "timestamp": "string"
    }
  ],
  "analysis_summary": {
    "total_brands": "integer",
    "most_mentioned_brand": "string",
    "brand_diversity_score": "float",
    "commercial_content_ratio": "float"
  }
}
```

### 8.3 LLM Prompt设计要求
- **多语言支持**: 支持英语、中文、西班牙语等多种语言的品牌识别
- **上下文理解**: 能够理解隐式品牌提及和产品植入
- **准确性要求**: 品牌识别准确率需达到85%以上
- **实时性要求**: 单个视频分析时间不超过30秒

---

## 九、 数据API接口需求框架

### 9.1 核心接口列表
- `GET /api/influencer/{id}/overview` - 获取达人总览数据
- `GET /api/influencer/{id}/content` - 获取内容数据
- `GET /api/influencer/{id}/audience` - 获取受众数据
- `GET /api/influencer/{id}/brands` - 获取品牌数据
- `GET /api/influencer/{id}/platforms` - 获取多平台数据
- `POST /api/influencer/{id}/analyze` - 触发品牌分析

### 9.2 数据同步需求
- **实时同步**: 粉丝数、最新视频等核心指标的实时更新
- **定时同步**: 历史数据、受众分析等的定时批量更新
- **增量同步**: 只同步变化的数据，提高效率

### 9.3 接口性能要求
- **响应时间**: 接口响应时间不超过2秒
- **并发支持**: 支持100个并发请求
- **数据缓存**: 对重复查询的数据进行缓存优化

---

## 十、 实现优先级建议

### 10.1 第一阶段 (P0)
- 数据总览模块实现
- 基础图表可视化
- 多平台账号关联展示

### 10.2 第二阶段 (P1)
- 内容数据和受众数据模块
- 品牌提及LLM分析功能
- 高级图表交互功能

### 10.3 第三阶段 (P2)
- 数据API接口完善
- 实时数据同步
- 移动端适配优化
