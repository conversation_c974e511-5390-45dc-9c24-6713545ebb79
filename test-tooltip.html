<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工具提示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .audience-analysis-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .audience-chart-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .card-header h4 {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin: 0 0 20px 0;
        }
        
        .horizontal-bar-section {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .horizontal-bar-container {
            width: 100%;
            padding: 0 92px 0 80px;
        }
        
        .horizontal-bar {
            display: flex;
            width: 100%;
            height: 24px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .bar-segment {
            height: 100%;
            transition: all 0.3s ease;
            position: relative;
            cursor: pointer;
        }
        
        .bar-segment:hover {
            opacity: 0.8;
            transform: scaleY(1.1);
        }
        
        /* 工具提示样式 */
        .tooltip {
            position: absolute;
            background-color: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
            z-index: 1000;
            pointer-events: none;
            opacity: 0;
            transform: translateY(-100%) translateX(-50%);
            transition: opacity 0.2s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
        
        .tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 5px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.9);
        }
        
        .tooltip.show {
            opacity: 1;
        }
        
        .tooltip-icon {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 6px;
            vertical-align: middle;
        }
        
        .region-detail-list,
        .language-detail-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-top: 10px;
        }
        
        .detail-item {
            display: grid;
            grid-template-columns: 80px 1fr 60px;
            align-items: center;
            gap: 12px;
        }
        
        .detail-label {
            font-size: 12px;
            color: #666;
            text-align: right;
            font-weight: 500;
        }
        
        .detail-bar {
            height: 20px;
            background-color: #f5f5f5;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .detail-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.8s ease;
        }
        
        .detail-percentage {
            font-size: 12px;
            font-weight: 600;
            color: #333;
            text-align: center;
        }
        
        .chart-note {
            font-size: 11px;
            color: #999;
            line-height: 1.4;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #f0f0f0;
        }
        
        .instruction {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>工具提示测试页面</h1>
        <div class="instruction">
            💡 将鼠标悬停在横向条形图的彩色分段上，查看详细的工具提示信息
        </div>
        
        <div class="audience-analysis-grid">
            <!-- 受众区域分布 -->
            <div class="audience-chart-card region-card">
                <div class="card-header">
                    <h4>受众区域</h4>
                </div>
                
                <!-- 横向条形图 -->
                <div class="horizontal-bar-section">
                    <div class="horizontal-bar-container">
                        <div class="horizontal-bar">
                            <div class="bar-segment" data-region="美国" data-percentage="68.7" style="background-color: #4285F4; width: 68.7%;"></div>
                            <div class="bar-segment" data-region="加拿大" data-percentage="9.8" style="background-color: #34A853; width: 9.8%;"></div>
                            <div class="bar-segment" data-region="英国" data-percentage="4.7" style="background-color: #FBBC04; width: 4.7%;"></div>
                            <div class="bar-segment" data-region="澳大利亚" data-percentage="4.3" style="background-color: #EA4335; width: 4.3%;"></div>
                            <div class="bar-segment" data-region="印度" data-percentage="3.5" style="background-color: #9C27B0; width: 3.5%;"></div>
                            <div class="bar-segment" data-region="德国" data-percentage="3.1" style="background-color: #FF9800; width: 3.1%;"></div>
                            <div class="bar-segment" data-region="其他" data-percentage="5.9" style="background-color: #607D8B; width: 5.9%;"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 详细地区列表 -->
                <div class="region-detail-list">
                    <div class="detail-item">
                        <span class="detail-label">美国</span>
                        <div class="detail-bar">
                            <div class="detail-fill" style="width: 68.7%; background-color: #4285F4;"></div>
                        </div>
                        <span class="detail-percentage">68.7%</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">加拿大</span>
                        <div class="detail-bar">
                            <div class="detail-fill" style="width: 9.8%; background-color: #34A853;"></div>
                        </div>
                        <span class="detail-percentage">9.8%</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">英国</span>
                        <div class="detail-bar">
                            <div class="detail-fill" style="width: 4.7%; background-color: #FBBC04;"></div>
                        </div>
                        <span class="detail-percentage">4.7%</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">澳大利亚</span>
                        <div class="detail-bar">
                            <div class="detail-fill" style="width: 4.3%; background-color: #EA4335;"></div>
                        </div>
                        <span class="detail-percentage">4.3%</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">印度</span>
                        <div class="detail-bar">
                            <div class="detail-fill" style="width: 3.5%; background-color: #9C27B0;"></div>
                        </div>
                        <span class="detail-percentage">3.5%</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">德国</span>
                        <div class="detail-bar">
                            <div class="detail-fill" style="width: 3.1%; background-color: #FF9800;"></div>
                        </div>
                        <span class="detail-percentage">3.1%</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">其他</span>
                        <div class="detail-bar">
                            <div class="detail-fill" style="width: 5.9%; background-color: #607D8B;"></div>
                        </div>
                        <span class="detail-percentage">5.9%</span>
                    </div>
                </div>
                
                <div class="chart-note">
                    受众地理数据基于过去30天的观看数据统计，通过IP地址分析得出地理位置分布
                </div>
            </div>

            <!-- 受众语言分布 -->
            <div class="audience-chart-card language-card">
                <div class="card-header">
                    <h4>受众语言</h4>
                </div>
                
                <!-- 横向条形图 -->
                <div class="horizontal-bar-section">
                    <div class="horizontal-bar-container">
                        <div class="horizontal-bar">
                            <div class="bar-segment" data-language="英语" data-percentage="84.5" style="background-color: #4285F4; width: 84.5%;"></div>
                            <div class="bar-segment" data-language="西班牙语" data-percentage="7.3" style="background-color: #FF9800; width: 7.3%;"></div>
                            <div class="bar-segment" data-language="葡萄牙语" data-percentage="6.4" style="background-color: #34A853; width: 6.4%;"></div>
                            <div class="bar-segment" data-language="其他语言" data-percentage="1.8" style="background-color: #607D8B; width: 1.8%;"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 详细语言列表 -->
                <div class="language-detail-list">
                    <div class="detail-item">
                        <span class="detail-label">英语</span>
                        <div class="detail-bar">
                            <div class="detail-fill" style="width: 84.5%; background-color: #4285F4;"></div>
                        </div>
                        <span class="detail-percentage">84.5%</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">西班牙语</span>
                        <div class="detail-bar">
                            <div class="detail-fill" style="width: 7.3%; background-color: #FF9800;"></div>
                        </div>
                        <span class="detail-percentage">7.3%</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">葡萄牙语</span>
                        <div class="detail-bar">
                            <div class="detail-fill" style="width: 6.4%; background-color: #34A853;"></div>
                        </div>
                        <span class="detail-percentage">6.4%</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">其他语言</span>
                        <div class="detail-bar">
                            <div class="detail-fill" style="width: 1.8%; background-color: #607D8B;"></div>
                        </div>
                        <span class="detail-percentage">1.8%</span>
                    </div>
                </div>
                
                <div class="chart-note">
                    受众语言数据基于用户设备语言设置和观看行为分析，通过机器学习算法识别用户语言偏好
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化工具提示
        function initializeTooltips() {
            const barSegments = document.querySelectorAll('.bar-segment');
            
            barSegments.forEach(segment => {
                // 创建工具提示元素
                const tooltip = document.createElement('div');
                tooltip.className = 'tooltip';
                document.body.appendChild(tooltip);
                
                // 鼠标进入事件
                segment.addEventListener('mouseenter', (e) => {
                    const region = segment.dataset.region || segment.dataset.language;
                    const percentage = segment.dataset.percentage;
                    const color = segment.style.backgroundColor;
                    
                    // 设置工具提示内容
                    tooltip.innerHTML = `
                        <span class="tooltip-icon" style="background-color: ${color};"></span>
                        <strong>${percentage}%</strong> ${region}
                    `;
                    
                    // 显示工具提示
                    tooltip.classList.add('show');
                    updateTooltipPosition(e, tooltip);
                });
                
                // 鼠标移动事件
                segment.addEventListener('mousemove', (e) => {
                    updateTooltipPosition(e, tooltip);
                });
                
                // 鼠标离开事件
                segment.addEventListener('mouseleave', () => {
                    tooltip.classList.remove('show');
                });
            });
        }
        
        // 更新工具提示位置
        function updateTooltipPosition(event, tooltip) {
            const rect = event.target.getBoundingClientRect();
            const tooltipRect = tooltip.getBoundingClientRect();
            
            // 计算位置
            let left = rect.left + (rect.width / 2);
            let top = rect.top - 10;
            
            // 防止工具提示超出屏幕边界
            if (left + tooltipRect.width / 2 > window.innerWidth) {
                left = window.innerWidth - tooltipRect.width / 2 - 10;
            }
            if (left - tooltipRect.width / 2 < 0) {
                left = tooltipRect.width / 2 + 10;
            }
            
            tooltip.style.left = left + 'px';
            tooltip.style.top = top + 'px';
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            initializeTooltips();
        });
    </script>
</body>
</html>
