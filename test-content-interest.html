<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>内容与兴趣分析测试</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #FF6B35;
            padding-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>内容与兴趣分析测试页面</h1>
        <p style="color: #666; margin-bottom: 20px;">测试新增的内容与兴趣分析模块功能</p>
        
        <!-- 内容与兴趣分析测试 -->
        <div class="test-section">
            <div class="test-title">内容与兴趣分析模块</div>
            
            <div class="content-interest-section">
                <div class="section-header">
                    <h3>
                        <i class="fas fa-heart"></i>
                        内容与兴趣
                    </h3>
                </div>
                
                <div class="content-interest-grid">
                    <!-- 内容分布 -->
                    <div class="audience-chart-card content-distribution-card">
                        <div class="card-header">
                            <h4>内容分布</h4>
                        </div>
                        
                        <div class="content-chart-container">
                            <canvas id="contentDistributionChart" width="400" height="300"></canvas>
                        </div>
                        
                        <div class="chart-note">
                            基于受众观看内容类型分析，反映受众对不同内容的偏好
                        </div>
                    </div>

                    <!-- 内容详情列表 -->
                    <div class="audience-chart-card content-details-card">
                        <div class="card-header">
                            <h4>详情</h4>
                            <div class="chart-controls">
                                <span class="total-count">百分比</span>
                            </div>
                        </div>
                        
                        <div class="content-details-list">
                            <div class="content-detail-item">
                                <div class="content-category">
                                    <div class="category-icon automotive"></div>
                                    <span class="category-name">汽车</span>
                                </div>
                                <div class="content-description">
                                    MrBeast的汽车主题内容获得观众高度关注，平均观看时长达7分14秒，平均每个视频获得超过1.8亿次观看，是其受众最感兴趣的内容之一。
                                </div>
                                <div class="content-percentage">32%</div>
                            </div>
                            
                            <div class="content-detail-item">
                                <div class="content-category">
                                    <div class="category-icon mobile"></div>
                                    <span class="category-name">手机游戏</span>
                                </div>
                                <div class="content-description">
                                    MrBeast在游戏领域的内容受到观众喜爱，其中手机游戏相关视频获得较高的互动率，观看完成度达到85%，显示了受众对此类内容的强烈兴趣。
                                </div>
                                <div class="content-percentage">18%</div>
                            </div>
                            
                            <div class="content-detail-item">
                                <div class="content-category">
                                    <div class="category-icon family"></div>
                                    <span class="category-name">家庭&时尚</span>
                                </div>
                                <div class="content-description">
                                    MrBeast的家庭和时尚相关内容获得稳定关注，平均每个视频获得超过7415万次观看，大部分观看时间集中在前5分钟，显示内容吸引力较强。
                                </div>
                                <div class="content-percentage">17%</div>
                            </div>
                            
                            <div class="content-detail-item">
                                <div class="content-category">
                                    <div class="category-icon beauty"></div>
                                    <span class="category-name">美妆&时尚</span>
                                </div>
                                <div class="content-description">
                                    MrBeast的美妆时尚类内容在年轻观众中表现出色，其中18-24岁观众占比达到71%，获得了较高的点赞率和分享率，展现出强大的影响力。
                                </div>
                                <div class="content-percentage">17%</div>
                            </div>
                            
                            <div class="content-detail-item">
                                <div class="content-category">
                                    <div class="category-icon vlog"></div>
                                    <span class="category-name">vlog</span>
                                </div>
                                <div class="content-description">
                                    MrBeast的日常vlog内容获得观众真实反馈，平均观看时长达4分7秒，观看完成度达94.4%，充分展现了观众对其个人生活内容的关注度。
                                </div>
                                <div class="content-percentage">17%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能说明 -->
        <div class="test-section">
            <div class="test-title">功能特性</div>
            <ul style="line-height: 1.6; color: #666;">
                <li><strong>环形图表：</strong>左侧显示内容分布的环形图，包含5个主要内容类别</li>
                <li><strong>详情列表：</strong>右侧显示每个内容类别的详细描述和百分比</li>
                <li><strong>交互功能：</strong>点击图表可高亮对应的详情项</li>
                <li><strong>响应式设计：</strong>在不同屏幕尺寸下自动调整布局</li>
                <li><strong>视觉一致性：</strong>与现有受众数据页签保持相同的设计风格</li>
                <li><strong>数据展示：</strong>包含具体的统计数据和说明文字</li>
            </ul>
        </div>

        <!-- 使用说明 -->
        <div class="test-section">
            <div class="test-title">使用说明</div>
            <div style="color: #666; line-height: 1.6;">
                <p><strong>1. 查看内容分布：</strong>左侧环形图显示了受众对不同内容类型的偏好分布，汽车内容占比最高(32%)。</p>
                <p><strong>2. 交互操作：</strong>点击环形图的任意扇形区域，右侧对应的详情项会高亮显示并自动滚动到视图中。</p>
                <p><strong>3. 详细信息：</strong>右侧列表提供了每个内容类别的详细描述，包括观看数据、用户行为等关键指标。</p>
                <p><strong>4. 数据来源：</strong>所有数据基于用户观看行为分析，反映真实的内容偏好趋势。</p>
            </div>
        </div>
    </div>

    <script>
        // 简化的内容分析初始化代码
        class ContentInterestTest {
            constructor() {
                this.initializeContentChart();
                this.bindEvents();
            }

            initializeContentChart() {
                const ctx = document.getElementById('contentDistributionChart').getContext('2d');
                
                this.contentChart = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: ['汽车', '手机游戏', '家庭&时尚', '美妆&时尚', 'vlog'],
                        datasets: [{
                            data: [32, 18, 17, 17, 17],
                            backgroundColor: [
                                '#4285F4', '#FF6B35', '#34A853', '#EA4335', '#9C27B0'
                            ],
                            borderColor: '#fff',
                            borderWidth: 3
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    padding: 20,
                                    usePointStyle: true,
                                    generateLabels: function(chart) {
                                        const data = chart.data;
                                        return data.labels.map((label, i) => ({
                                            text: `${label} ${data.datasets[0].data[i]}%`,
                                            fillStyle: data.datasets[0].backgroundColor[i],
                                            pointStyle: 'circle',
                                            index: i
                                        }));
                                    }
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return context.label + ': ' + context.parsed + '%';
                                    }
                                }
                            }
                        },
                        cutout: '60%',
                        onClick: (event, elements) => {
                            if (elements.length > 0) {
                                this.highlightContentItem(elements[0].index);
                            }
                        }
                    }
                });
            }

            highlightContentItem(index) {
                const items = document.querySelectorAll('.content-detail-item');
                items.forEach((item, i) => {
                    item.classList.remove('highlighted');
                    if (i === index) {
                        item.classList.add('highlighted');
                        item.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                    }
                });
            }

            bindEvents() {
                // 可以添加其他交互事件
                console.log('内容与兴趣分析模块已初始化');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            new ContentInterestTest();
        });
    </script>
</body>
</html>
