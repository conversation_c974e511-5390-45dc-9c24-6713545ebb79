<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>受众数据更新测试</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #FF6B35;
            padding-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>受众数据更新测试页面</h1>
        <p style="color: #666; margin-bottom: 20px;">测试年龄和性别分析模块的修改效果</p>
        
        <!-- 年龄和性别分布测试 -->
        <div class="test-section">
            <div class="test-title">年龄和性别分布（已更新）</div>
            <div class="demographics-grid">
                <!-- 年龄分布图表 -->
                <div class="audience-chart-card">
                    <div class="card-header">
                        <h4>年龄分布</h4>
                        <div class="chart-controls">
                            <span class="total-count">总计 100%</span>
                        </div>
                    </div>
                    <div class="age-chart-container">
                        <canvas id="ageDistributionChart" width="400" height="300"></canvas>
                    </div>
                    <div class="chart-note">
                        年龄数据基于用户账户信息和观看行为模式分析，显示主要受众年龄分布情况
                    </div>
                </div>

                <!-- 性别分布图表 -->
                <div class="audience-chart-card">
                    <div class="card-header">
                        <h4>性别分布</h4>
                        <div class="chart-controls">
                            <span class="total-count">总计 100%</span>
                        </div>
                    </div>
                    <div class="gender-chart-container">
                        <canvas id="genderDistributionChart" width="300" height="300"></canvas>
                    </div>
                    <div class="gender-stats-summary">
                        <div class="gender-stat-item">
                            <div class="gender-indicator male"></div>
                            <span class="gender-label">男性</span>
                            <span class="gender-percentage">52.4%</span>
                        </div>
                        <div class="gender-stat-item">
                            <div class="gender-indicator female"></div>
                            <span class="gender-label">女性</span>
                            <span class="gender-percentage">47.6%</span>
                        </div>
                    </div>
                    <div class="chart-note">
                        性别数据基于用户账户信息和内容偏好分析，男性受众略多于女性受众
                    </div>
                </div>
            </div>
        </div>

        <!-- 修改说明 -->
        <div class="test-section">
            <div class="test-title">修改说明</div>
            <ul style="line-height: 1.6; color: #666;">
                <li><strong>年龄分布图表：</strong>移除了图表下方的年龄统计表格，保留了柱状图和说明文字</li>
                <li><strong>性别分布图表：</strong>移除了"未知"性别类别，只保留男性(52.4%)和女性(47.6%)两个分类</li>
                <li><strong>数据重新计算：</strong>确保男性和女性的百分比总和为100%</li>
                <li><strong>交互功能：</strong>保留了图表的tooltip等交互功能</li>
                <li><strong>样式优化：</strong>移除了相关的CSS样式，确保界面布局协调</li>
            </ul>
        </div>
    </div>

    <script>
        // 简化的图表初始化代码
        class TestDashboard {
            constructor() {
                this.initializeAgeChart();
                this.initializeGenderChart();
            }

            initializeAgeChart() {
                const ctx = document.getElementById('ageDistributionChart').getContext('2d');
                
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['13-17', '18-24', '25-34', '35-44', '45-54', '55-64', '65+'],
                        datasets: [{
                            label: '受众比例',
                            data: [8.2, 24.3, 22.1, 18.7, 14.2, 8.9, 3.6],
                            backgroundColor: [
                                '#FF6B35', '#FF8A65', '#FFB74D', '#81C784', 
                                '#64B5F6', '#9575CD', '#F06292'
                            ],
                            borderRadius: 4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { display: false },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return '占比: ' + context.parsed.y + '%';
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 30,
                                ticks: {
                                    callback: function(value) {
                                        return value + '%';
                                    }
                                }
                            }
                        }
                    }
                });
            }

            initializeGenderChart() {
                const ctx = document.getElementById('genderDistributionChart').getContext('2d');
                
                new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: ['男性', '女性'],
                        datasets: [{
                            data: [52.4, 47.6],
                            backgroundColor: ['#4285F4', '#EA4335'],
                            borderColor: '#fff',
                            borderWidth: 3
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    padding: 20,
                                    usePointStyle: true
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return context.label + ': ' + context.parsed + '%';
                                    }
                                }
                            }
                        },
                        cutout: '60%'
                    }
                });
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            new TestDashboard();
        });
    </script>
</body>
</html>
